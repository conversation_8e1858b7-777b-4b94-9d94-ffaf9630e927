{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "storybook": {"cache": false, "persistent": true}, "db:generate": {}, "db:push:test": {}, "test": {}}, "globalEnv": ["VERCEL_PROJECT_PRODUCTION_URL", "NEXTAUTH_SECRET", "BUILD_ID", "SIGNUP_BUILD_ID", "DATABASE_URL", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "FACEBOOK_CLIENT_ID", "GET_STREAM_APP_SECRET", "INVITE_TOKEN_SECRET", "KNOCK_NOTIFICATION_FEED_ID", "KNOCK_PUBLIC_API_KEY", "KNOCK_SIGNING_KEY", "KNOCK_SECRET_API_KEY", "RESET_PASSWORD_TOKEN_SECRET", "STRIPE_APPLICATION_FEE_BASIC", "STRIPE_APPLICATION_FEE_PRO", "STRIPE_APPLICATION_FEE_ENTERPRISE", "STRIPE_PRODUCT_ID_INSPIRED", "STRIPE_PRODUCT_ID_INMOTION", "STRIPE_PRODUCT_ID_INFINITE", "STRIPE_PRODUCT_ID_INSPIRED_TEST", "STRIPE_PRODUCT_ID_INMOTION_TEST", "STRIPE_PRODUCT_ID_INFINITE_TEST", "STRIPE_SECRET_KEY", "STRIPE_SECRET_KEY_TEST", "STRIPE_WEBHOOK_MEMBERUP_SECRET", "STRIPE_WEBHOOK_MEMBERUP_SECRET_TEST", "STRIPE_WEBHOOK_MEMBERSHIP_SECRET", "STRIPE_WEBHOOK_MEMBERSHIP_SECRET_TEST", "STRIPE_INTERNAL_DISCOUNT_ID", "CLOUD_API_SECRET", "VERCEL_PROJECT_ID", "VERCEL_TEAM_ID", "VERCEL_MEMBERUP_TOKEN", "CYPRESS_RECORD_KEY_MEMBERUP", "CYPRESS_RECORD_KEY_MEMBERUP_SIGNUP", "CYPRESS_PROJECT_ID_MEMBERUP", "CYPRESS_PROJECT_ID_MEMBERUP_SIGNUP", "SPARK_CRON_SECRET_KEY", "SENDER_EMAIL", "CHECKLY_API_KEY", "MERGENT_API_KEY", "SENDGRID_API_KEY", "INNGEST_EVENT_KEY", "CHECKLY_ACCOUNT_ID", "GETSTREAM_PASSWORD", "GETSTREAM_USERNAME", "INNGEST_SIGNING_KEY", "REWARDFUL_API_SECRET", "PLAYWRIGHT_APP_DOMAIN", "ACTIVE_CAMPAIGN_API_KEY", "ACTIVE_CAMPAIGN_API_URL", "PLANETSCALE_BRANCH_NAME", "PLAYWRIGHT_APP_PROTOCOL", "PLANETSCALE_SERVICE_TOKEN", "PLANETSCALE_SERVICE_TOKEN_ID", "PLANETSCALE_ORGANIZATION_NAME", "PLAYWRIGHT_COMMUNITY_1_DOMAIN", "ACTIONS_REPLACE_STRIPE_SETTINGS", "ACTIVE_CAMPAIGN_CONTACT_LIST_ID", "DOPPLER_CONFIG", "DOPPLER_ENVIRONMENT", "DOPPLER_PROJECT", "PRODUCTION_GETSTREAM_APP_SECRET", "PRODUCTION_GETSTREAM_APP_KEY", "ACTIVE_CAMPAIGN_ENABLED", "PLAYWRIGHT_COMMUNITY_1_ADMIN_USER_1_EMAIL", "PLAYWRIGHT_COMMUNITY_1_ADMIN_USER_1_PASSWORD", "PLAYWRIGHT_COMMUNITY_1_MEMBER_USER_1_EMAIL", "PLAYWRIGHT_COMMUNITY_1_MEMBER_USER_1_PASSWORD", "PLAYWRIGHT_USE_STORAGE_STATE", "DATABASE_HOST", "DATABASE_PASSWORD", "DATABASE_USERNAME", "FREE_TRIAL_DAYS", "STAGING_GET_STREAM_APP_KEY", "STAGING_GET_STREAM_APP_SECRET", "SENTRY_AUTH_TOKEN", "GEEKFLARE_API_KEY", "NODE_ENV", "NEXT_PUBLIC_SENTRY_DSN"]}