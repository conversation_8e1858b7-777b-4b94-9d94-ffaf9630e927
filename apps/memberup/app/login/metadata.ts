import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Login to MemberUp',
  description: 'Sign in to your MemberUp account.',
  alternates: {
    canonical: '/login',
  },
  openGraph: {
    title: 'Login to MemberUp',
    description: 'Sign in to your MemberUp account.',
    type: 'website',
    siteName: 'MemberUp',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    title: 'Login to MemberUp',
    description: 'Sign in to your MemberUp account.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
