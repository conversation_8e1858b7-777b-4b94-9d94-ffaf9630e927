import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'

import { ScrollArea } from '@/components/ui'
import { Carousel, CarouselContent } from '@/components/ui/carousel'
import { Pill } from '@/components/ui/pill'
import { getMetadataBase } from '@/lib/metadata/metadata'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'

export const metadata: Metadata = {
  metadataBase: getMetadataBase(),
}

const features = [
  'Community',
  'Courses',
  'Payments',
  'Events',
  'Messenger',
  'Video hosting',
  'Analytics',
  'Member Directory',
  'Custom Branding',
  'Rich Profiles',
  'Moderation',
  'Search',
  'In-App Notifications',
]

export default function CreatorOnboardingLayout({ children }: { children: React.ReactNode }) {
  return (
    <ScrollArea className="absolute h-full w-full bg-black-700">
      <div className="flex w-full justify-center overflow-hidden">
        <div className="flex h-full min-h-svh max-w-[1364px] flex-col justify-start lg:justify-center lg:py-4">
          <Image
            className="mb-4 mt-4 hidden w-fit self-center pl-4 md:mb-[7.0625rem] md:mt-8 md:block md:self-auto md:pl-8 lg:mb-16 lg:mt-0 lg:pl-16"
            src={memberupLogo}
            width={170}
            height={25}
            alt="MemberUp"
          />

          <Link href="/community"></Link>

          <div className="flex w-full flex-col md:flex-row md:justify-between">
            <div className="hidden w-full min-w-0 grow pr-0 md:block md:w-auto md:pr-7">
              <div className="mb-4 mt-4 whitespace-normal px-4 text-center text-[1.75rem] font-semibold leading-[1.15] text-white-500 sm:text-[3rem] md:mt-0 md:px-0 md:pl-8 md:text-left lg:pl-16 lg:text-[3.625rem]">
                Where creators + brands build &#8203;
                <span className="bg-gradient-to-r from-[#DCBCFF] to-[#B673FE] bg-clip-text text-transparent">
                  extraordinary
                </span>{' '}
                &#8203;communities.
              </div>
              <div className="px-4 py-4 text-center text-ssm text-white-500 sm:text-base md:px-8 md:text-left lg:px-16">
                Make money building a community around a topic you love. Host your online course, community, events, and
                payments, all in one place, without the tech headache.
              </div>
              <div className="relative mt-2 h-[6.875rem] md:hidden">
                <div className="absolute w-full overflow-hidden">
                  <Carousel className="w-full">
                    <CarouselContent className="-ml-1.5">
                      <div className="flex flex-col gap-3 pl-1.5">
                        <div className="flex gap-3">
                          {features.slice(0, Math.ceil(features.length / 2)).map((feature) => (
                            <Pill key={feature} className="whitespace-nowrap">
                              {feature}
                            </Pill>
                          ))}
                        </div>
                        <div className="flex gap-3">
                          {features.slice(Math.ceil(features.length / 2)).map((feature) => (
                            <Pill key={feature} className="whitespace-nowrap">
                              {feature}
                            </Pill>
                          ))}
                        </div>
                      </div>
                    </CarouselContent>
                  </Carousel>
                </div>
              </div>
              <div className="hidden px-4 md:block md:px-8 lg:px-16">
                <div className="flex flex-wrap items-start justify-start gap-3 py-6 md:pb-0 md:pt-8 lg:pt-14">
                  {features.map((feature) => (
                    <Pill key={feature}>{feature}</Pill>
                  ))}
                </div>
              </div>
            </div>
            <div className="relative mt-8 w-full shrink-0 px-4 md:mr-8 md:mt-8 md:w-[23.375rem] md:px-0 lg:mr-16 lg:mt-0 lg:w-[28.0625rem]">
              {children}
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  )
}
