import Script from 'next/script'

import { CreateCommunityClient } from '@/components/community/create-community-client'
import { getMetadataBase } from '@/lib/metadata/metadata'

function CreateCommunityPage() {
  const baseUrl = getMetadataBase()

  // Structured data for SEO: generates JSON-LD schema.org metadata to help search engines understand this page as a "Create Community" landing page.
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Create Your Community on MemberUp',
    description: 'Start building your community on MemberUp!',
    url: new URL('/create-community', baseUrl).toString(),
    image: new URL('/assets/default/logos/memberup-logo.png', baseUrl).toString(),
    publisher: {
      '@type': 'Organization',
      name: 'MemberUp',
      logo: new URL('/assets/default/logos/memberup-logo.png', baseUrl).toString(),
    },
  }

  const safeJson = JSON.stringify(jsonLd).replace(/</g, '\\u003c')

  return (
    <>
      <Script id="json-ld" type="application/ld+json" dangerouslySetInnerHTML={{ __html: safeJson }} />
      <CreateCommunityClient />
    </>
  )
}

export default CreateCommunityPage
