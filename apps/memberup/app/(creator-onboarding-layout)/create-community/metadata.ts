import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Create Your Community',
  description: 'Start building your community on MemberUp!',
  alternates: {
    canonical: '/create-community',
  },
  openGraph: {
    title: 'Create Your Community on MemberUp',
    description: 'Start building your community on MemberUp!',
    type: 'website',
    siteName: 'MemberUp',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    title: 'Create Your Community on MemberUp',
    description: 'Start building your community on MemberUp!',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
