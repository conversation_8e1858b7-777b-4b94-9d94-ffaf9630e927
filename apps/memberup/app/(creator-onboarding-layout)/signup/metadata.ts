import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Sign Up for MemberUp',
  description: 'Create your account and start building your community on MemberUp!',
  alternates: {
    canonical: '/signup',
  },
  openGraph: {
    title: 'Sign Up for MemberUp',
    description: 'Create your account and start building your community on MemberUp!',
    type: 'website',
    siteName: 'MemberUp',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    title: 'Sign Up for MemberUp',
    description: 'Create your account and start building your community on MemberUp!',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
