import { Metadata } from 'next'

import { getMetadataBase } from '@/lib/metadata/metadata'

export const metadata: Metadata = {
  metadataBase: getMetadataBase(),
  title: {
    default: 'MemberUp',
    template: '%s',
  },
  description: 'Where creators & brands build extraordinary communities.',
  openGraph: {
    type: 'website',
    siteName: 'MemberUp',
    title: 'MemberUp',
    description: 'Where creators & brands build extraordinary communities.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MemberUp',
    description: 'Where creators & brands build extraordinary communities.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
  },
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  other: {
    preconnect: ['https://fonts.googleapis.com', 'https://fonts.gstatic.com', 'https://js.stripe.com'],
  },
}
