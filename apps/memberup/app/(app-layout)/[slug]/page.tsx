import { Metadata } from 'next'

import { generateCommunityMetadata, generateProfileMetadata, isProfile } from './metadata'
import { Feed } from '@/components/feed/feed'
import { UserProfileServerComponent } from '@/components/profile/UserProfileServerComponent'

export default async function Page(props: { params: Promise<{ slug: string }> }) {
  const params = await props.params

  if (isProfile(params.slug)) {
    return <UserProfileServerComponent params={params} />
  }

  return <Feed />
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  if (isProfile(slug)) {
    return generateProfileMetadata(slug)
  }
  return generateCommunityMetadata(slug)
}
