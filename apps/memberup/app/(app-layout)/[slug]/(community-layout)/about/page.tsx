import Link from 'next/link'

import CommunityAboutPage from '@/components/community/about-page/about-page'
import { CommunityDetails } from '@/components/community/community-details'

const CommunityAboutPageLayout = async () => (
  <div className="flex w-full flex-col gap-5">
    <div className="mt-[-16px] flex flex-col-reverse items-start md:mt-0 md:flex-row md:space-x-6">
      <CommunityAboutPage />
      <CommunityDetails />
    </div>

    <Link
      href="/privacy-policy"
      target="_blank"
      rel="noopener noreferrer"
      className="mb-5 hidden text-sm text-black-200 dark:text-black-100 md:block"
    >
      Privacy and terms
    </Link>
  </div>
)

export default CommunityAboutPageLayout
