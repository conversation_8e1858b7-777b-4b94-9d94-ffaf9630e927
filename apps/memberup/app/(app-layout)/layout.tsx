import { Metadata } from 'next'
import { headers } from 'next/headers'
import { notFound, redirect } from 'next/navigation'
import Script from 'next/script'

import { auth } from '@/auth'
import AppLayout from '@/components/layout/AppLayout'
import { StreamChatProvider } from '@/components/providers/StreamChatProvider'
import { canAccessCommunityPath } from '@/lib/authorization'
import { cloudinaryLoader, generateCloudinaryPretransforms } from '@/lib/cloudinary'
import { nonCommunityPathnames } from '@/lib/constants'
import { getBaseMetadataDescription, getMetadataBase } from '@/lib/metadata/metadata'
import { getCachedAuthenticatedUserData } from '@/lib/server-components/users'
import { getCachedCommunityData } from '@/lib/server/communities'

export const metadata: Metadata = {
  metadataBase: getMetadataBase(),
}

export default async function AppLayoutWrapper({ children }: { children: React.ReactNode }) {
  const requestHeaders = await headers()
  const communitySlug = requestHeaders.get('x-community-slug')
  const pathname = requestHeaders.get('x-pathname')
  let membershipData = null

  const pathnameParts = pathname.split('/')

  if (!pathname.startsWith('/@') && !nonCommunityPathnames.includes(`/${pathnameParts[1]}`)) {
    membershipData = await getCachedCommunityData(communitySlug)

    if (!membershipData) {
      notFound()
    }

    const session = await auth()
    let userData = null

    if (session?.user?.id) {
      userData = await getCachedAuthenticatedUserData(session.user.id)
    }

    if (!canAccessCommunityPath(userData?.user, membershipData, pathname)) {
      redirect(`/${communitySlug}/about`)
    }
  }

  const baseUrl = getMetadataBase()

  const logoImageUrl = membershipData?.membership_setting?.favicon
    ? cloudinaryLoader({
        loaderOptions: {
          src: membershipData?.membership_setting?.favicon,
          width: 400,
          quality: 90,
        },
        preTransforms: generateCloudinaryPretransforms(membershipData?.membership_setting?.favicon_crop_area),
      })
    : null

  // Structured data for SEO: generates JSON-LD schema.org metadata for either a community (Organization) or the main site (WebSite)
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': membershipData ? 'Organization' : 'WebSite',
    name: membershipData ? membershipData.name : 'MemberUp',
    description: getBaseMetadataDescription(membershipData),
    url: new URL(communitySlug ? `/${communitySlug}` : '/', baseUrl).toString(),
    logo: logoImageUrl ?? new URL('/assets/default/logos/memberup-logo.png', baseUrl).toString(),
    ...(membershipData && {
      image: logoImageUrl ?? undefined,
      foundingDate: membershipData.created_at,
    }),
  }

  const safeJson = JSON.stringify(jsonLd).replace(/</g, '\\u003c')

  return (
    <StreamChatProvider>
      <AppLayout>
        <Script id="json-ld" type="application/ld+json" dangerouslySetInnerHTML={{ __html: safeJson }} />
        {children}
      </AppLayout>
    </StreamChatProvider>
  )
}
