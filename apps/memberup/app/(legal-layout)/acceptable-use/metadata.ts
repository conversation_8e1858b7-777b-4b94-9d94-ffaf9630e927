import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Acceptable use policy',
  description: 'Guidelines and policies for acceptable use of MemberUp services and platform.',
  openGraph: {
    title: 'Acceptable use policy',
    description: 'Guidelines and policies for acceptable use of MemberUp services and platform.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    title: 'Acceptable use policy',
    description: 'Guidelines and policies for acceptable use of MemberUp services and platform.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
