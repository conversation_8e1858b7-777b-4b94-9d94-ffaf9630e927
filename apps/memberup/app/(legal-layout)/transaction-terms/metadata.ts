import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Transaction terms',
  description: 'Terms and conditions for transactions and payments on the MemberUp platform.',
  openGraph: {
    description: 'Terms and conditions for transactions and payments on the MemberUp platform.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    description: 'Terms and conditions for transactions and payments on the MemberUp platform.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
