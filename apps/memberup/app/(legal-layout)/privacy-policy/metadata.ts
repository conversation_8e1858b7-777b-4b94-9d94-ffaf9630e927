import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Privacy Policy', // Will be used for all title variants
  description: 'Learn about how MemberUp collects, uses, shares, and protects your personal data through our services.',
  openGraph: {
    description:
      'Learn about how MemberUp collects, uses, shares, and protects your personal data through our services.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    description:
      'Learn about how MemberUp collects, uses, shares, and protects your personal data through our services.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
