import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Terms and conditions',
  description: 'Read our terms and conditions for using MemberUp services and platform.',
  openGraph: {
    description: 'Read our terms and conditions for using MemberUp services and platform.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    description: 'Read our terms and conditions for using MemberUp services and platform.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
