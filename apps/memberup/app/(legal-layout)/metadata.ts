import { Metadata } from 'next'

export const metadata: Metadata = {
  title: {
    template: '%s | MemberUp',
    default: 'Legal | MemberUp',
  },
  description: 'Legal information, terms of service, and privacy policy for MemberUp.',
  openGraph: {
    title: {
      template: '%s | MemberUp',
      default: 'Legal | MemberUp',
    },
    description: 'Legal information, terms of service, and privacy policy for MemberUp.',
    type: 'website',
    siteName: 'MemberUp',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        width: 1200,
        height: 630,
        alt: 'MemberUp Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@member_up',
    creator: '@member_up',
    title: {
      template: '%s | MemberUp',
      default: 'Legal | MemberUp',
    },
    description: 'Legal information, terms of service, and privacy policy for MemberUp.',
    images: [
      {
        url: '/assets/default/logos/memberup-logo.png',
        alt: 'MemberUp Logo',
      },
    ],
  },
}
