import { Metadata } from 'next'
import Script from 'next/script'
import { ReactNode } from 'react'

import AppLayout from '@/components/layout/AppLayout'
import { BackToTop } from '@/components/ui/back-to-top'
import { getMetadataBase } from '@/lib/metadata/metadata'

export const metadata: Metadata = {
  metadataBase: getMetadataBase(),
}

export default function LegalLayout({ children }: { children: ReactNode }) {
  // Structured data for SEO: generates JSON-LD schema.org metadata describing MemberUp as an organization for legal pages.
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'MemberUp',
    description: 'Legal information, terms of service, and privacy policy for MemberUp.',
    url: getMetadataBase().toString(),
    logo: '/assets/default/logos/memberup-logo.png',
  }

  const safeJson = JSON.stringify(jsonLd).replace(/</g, '\\u003c')

  return (
    <>
      <Script id="legal-schema" type="application/ld+json" dangerouslySetInnerHTML={{ __html: safeJson }} />
      <AppLayout>
        <div className="mobile-padded-content-container flex min-h-screen">
          <main className="flex-1 pb-8 xl:pt-1">
            <div className="flex w-full justify-center xl:block">{children}</div>
          </main>
        </div>
        <BackToTop />
      </AppLayout>
    </>
  )
}
