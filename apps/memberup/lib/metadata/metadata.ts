export const getBaseMetadataTitle = (communityData?: { name: string }) => {
  return communityData ? `${communityData.name}` : 'MemberUp'
}

export const getBaseMetadataDescription = (communityData?: { name: string }) => {
  return communityData ? `${communityData.name}` : 'Where creators & brands build extraordinary communities'
}

export const getMetadataBase = (): URL => {
  if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    return new URL(`https://${process.env.NEXT_PUBLIC_VERCEL_URL}`)
  }

  return new URL('http://localhost:3000')
}
