import { Knock<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@knocklabs/react'
import { useEffect, useRef } from 'react'

import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'

export function Body({ children }: { children: React.ReactNode }) {
  const communityColors = useStore((state) => state.community.colors)
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)
  const themeMode = useStore((state) => state.ui.themeMode)
  const user = useStore((state) => state.auth.user)
  const knockToken = useStore((state) => state.auth.knockToken)
  const userLastActivityUpdateIntervalRef = useRef(null)

  useEffect(() => {
    if (user && !userLastActivityUpdateIntervalRef.current) {
      userLastActivityUpdateIntervalRef.current = setInterval(() => {
        fetch('/api/user/update-last-activity', { method: 'POST' })
      }, 60000)
    }

    if (!user && userLastActivityUpdateIntervalRef.current) {
      clearInterval(userLastActivityUpdateIntervalRef.current)
      userLastActivityUpdateIntervalRef.current = null
    }

    return () => {
      if (userLastActivityUpdateIntervalRef.current) {
        clearInterval(userLastActivityUpdateIntervalRef.current)
        userLastActivityUpdateIntervalRef.current = null
      }
    }
  }, [user])

  // Added suppressHydrationWarning because this body tag contains dynamic styles and classes
  // that may differ between server and client due to theme and color calculations.

  return (
    <body
      className={cn(
        'app-router h-full font-sans',
        themeMode,
        isDarkTheme ? 'bg-black-700 text-white-500' : 'bg-grey-100 text-black-500',
      )}
      suppressHydrationWarning
    >
      <style>{`
        :root {
          --community-primary: ${communityColors.primaryHSL};
          --community-secondary: ${communityColors.secondaryHSL};
          --community-logo: color-mix(in srgb, hsl(${communityColors.primaryHSL}), black 40%);
        }
      `}</style>
      {knockToken ? (
        <KnockProvider apiKey={process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY} userId={user?.id} userToken={knockToken}>
          <KnockFeedProvider
            defaultFeedOptions={{
              page_size: 14,
            }}
            feedId={process.env.NEXT_PUBLIC_KNOCK_NOTIFICATION_FEED_ID}
          >
            {children}
          </KnockFeedProvider>
        </KnockProvider>
      ) : (
        children
      )}
    </body>
  )
}
